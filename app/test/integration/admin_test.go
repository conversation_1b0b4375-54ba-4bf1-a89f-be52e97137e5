//go:build integration

package integration

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/information-sharing-networks/signalsd/app/internal/auth"
	"github.com/information-sharing-networks/signalsd/app/internal/database"
)

// TestGetServiceAccountsEndpoint tests the /api/admin/service-accounts endpoint including:
// - Get all service accounts (no query parameters)
// - Get service account by account ID
// - Get service account by client ID
// - Get service account by email and organization combination
// - Parameter validation and error handling
func TestGetServiceAccountsEndpoint(t *testing.T) {
	ctx := context.Background()
	testDB := setupTestDatabase(t, ctx)
	testEnv := setupTestEnvironment(testDB)

	// Start server
	testURL := getTestDatabaseURL()
	baseURL, stopServer := startInProcessServer(t, ctx, testEnv.dbConn, testURL)
	defer stopServer()

	t.Log("Creating test data...")

	// Create test accounts
	ownerAccount := createTestAccount(t, ctx, testEnv.queries, "owner", "user", "<EMAIL>")
	adminAccount := createTestAccount(t, ctx, testEnv.queries, "admin", "user", "<EMAIL>")
	memberAccount := createTestAccount(t, ctx, testEnv.queries, "member", "user", "<EMAIL>")

	// Create test service accounts with different organizations and emails
	serviceAccount1 := createTestServiceAccount(t, ctx, testEnv.queries, "<EMAIL>", "Organization One")
	serviceAccount2 := createTestServiceAccount(t, ctx, testEnv.queries, "<EMAIL>", "Organization Two")
	serviceAccount3 := createTestServiceAccount(t, ctx, testEnv.queries, "<EMAIL>", "Organization One")

	// Get access tokens for different roles
	ownerToken := getAccessToken(t, testEnv.authService, ownerAccount.ID)
	adminToken := getAccessToken(t, testEnv.authService, adminAccount.ID)
	memberToken := getAccessToken(t, testEnv.authService, memberAccount.ID)

	t.Run("authorization tests", func(t *testing.T) {
		tests := []struct {
			name           string
			token          string
			expectedStatus int
			description    string
		}{
			{
				name:           "owner_can_access",
				token:          ownerToken,
				expectedStatus: http.StatusOK,
				description:    "Owner should be able to access service accounts",
			},
			{
				name:           "admin_can_access",
				token:          adminToken,
				expectedStatus: http.StatusOK,
				description:    "Admin should be able to access service accounts",
			},
			{
				name:           "member_cannot_access",
				token:          memberToken,
				expectedStatus: http.StatusForbidden,
				description:    "Member should not be able to access service accounts",
			},
			{
				name:           "no_token_unauthorized",
				token:          "",
				expectedStatus: http.StatusUnauthorized,
				description:    "Request without token should be unauthorized",
			},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				response := makeGetServiceAccountsRequest(t, baseURL, tt.token, "")
				defer response.Body.Close()

				if response.StatusCode != tt.expectedStatus {
					t.Errorf("Expected status %d, got %d. %s", tt.expectedStatus, response.StatusCode, tt.description)
				}
			})
		}
	})

	t.Run("get all service accounts", func(t *testing.T) {
		response := makeGetServiceAccountsRequest(t, baseURL, ownerToken, "")
		defer response.Body.Close()

		if response.StatusCode != http.StatusOK {
			t.Fatalf("Expected status 200, got %d", response.StatusCode)
		}

		var serviceAccounts []map[string]any
		if err := json.NewDecoder(response.Body).Decode(&serviceAccounts); err != nil {
			t.Fatalf("Failed to decode response: %v", err)
		}

		// Should have 3 service accounts
		if len(serviceAccounts) != 3 {
			t.Errorf("Expected 3 service accounts, got %d", len(serviceAccounts))
		}

		// Verify response structure
		for _, sa := range serviceAccounts {
			requiredFields := []string{"account_id", "client_id", "client_contact_email", "client_organization", "created_at", "updated_at"}
			for _, field := range requiredFields {
				if _, exists := sa[field]; !exists {
					t.Errorf("Missing required field '%s' in service account response", field)
				}
			}
		}
	})

	t.Run("get service account by account ID", func(t *testing.T) {
		queryParams := fmt.Sprintf("id=%s", serviceAccount1.AccountID)
		response := makeGetServiceAccountsRequest(t, baseURL, ownerToken, queryParams)
		defer response.Body.Close()

		if response.StatusCode != http.StatusOK {
			t.Fatalf("Expected status 200, got %d", response.StatusCode)
		}

		var serviceAccount map[string]any
		if err := json.NewDecoder(response.Body).Decode(&serviceAccount); err != nil {
			t.Fatalf("Failed to decode response: %v", err)
		}

		// Verify it's the correct service account
		if serviceAccount["account_id"].(string) != serviceAccount1.AccountID.String() {
			t.Errorf("Expected account ID %s, got %s", serviceAccount1.AccountID, serviceAccount["account_id"])
		}
		if serviceAccount["client_contact_email"].(string) != serviceAccount1.ClientContactEmail {
			t.Errorf("Expected email %s, got %s", serviceAccount1.ClientContactEmail, serviceAccount["client_contact_email"])
		}
	})

	t.Run("get service account by client ID", func(t *testing.T) {
		queryParams := fmt.Sprintf("client_id=%s", serviceAccount2.ClientID)
		response := makeGetServiceAccountsRequest(t, baseURL, ownerToken, queryParams)
		defer response.Body.Close()

		if response.StatusCode != http.StatusOK {
			t.Fatalf("Expected status 200, got %d", response.StatusCode)
		}

		var serviceAccount map[string]any
		if err := json.NewDecoder(response.Body).Decode(&serviceAccount); err != nil {
			t.Fatalf("Failed to decode response: %v", err)
		}

		// Verify it's the correct service account
		if serviceAccount["client_id"].(string) != serviceAccount2.ClientID {
			t.Errorf("Expected client ID %s, got %s", serviceAccount2.ClientID, serviceAccount["client_id"])
		}
		if serviceAccount["client_organization"].(string) != serviceAccount2.ClientOrganization {
			t.Errorf("Expected organization %s, got %s", serviceAccount2.ClientOrganization, serviceAccount["client_organization"])
		}
	})

	t.Run("get service account by email and organization", func(t *testing.T) {
		params := map[string]string{
			"client_email":        serviceAccount3.ClientContactEmail,
			"client_organization": serviceAccount3.ClientOrganization,
		}
		response := makeGetServiceAccountsRequestWithParams(t, baseURL, ownerToken, params)
		defer response.Body.Close()

		if response.StatusCode != http.StatusOK {
			// Debug: print response body to understand the error
			var errorResponse map[string]any
			if err := json.NewDecoder(response.Body).Decode(&errorResponse); err != nil {
				t.Logf("Failed to decode error response: %v", err)
			} else {
				t.Logf("Error response: %+v", errorResponse)
			}
			t.Fatalf("Expected status 200, got %d", response.StatusCode)
		}

		var serviceAccount map[string]any
		if err := json.NewDecoder(response.Body).Decode(&serviceAccount); err != nil {
			t.Fatalf("Failed to decode response: %v", err)
		}

		// Verify it's the correct service account
		if serviceAccount["client_contact_email"].(string) != serviceAccount3.ClientContactEmail {
			t.Errorf("Expected email %s, got %s", serviceAccount3.ClientContactEmail, serviceAccount["client_contact_email"])
		}
		if serviceAccount["client_organization"].(string) != serviceAccount3.ClientOrganization {
			t.Errorf("Expected organization %s, got %s", serviceAccount3.ClientOrganization, serviceAccount["client_organization"])
		}
	})

	t.Run("parameter validation tests", func(t *testing.T) {
		tests := []struct {
			name           string
			queryParams    string
			expectedStatus int
			description    string
		}{
			{
				name:           "invalid_account_id_format",
				queryParams:    "id=invalid-uuid",
				expectedStatus: http.StatusBadRequest,
				description:    "Invalid UUID format should return 400",
			},
			{
				name:           "nonexistent_account_id",
				queryParams:    fmt.Sprintf("id=%s", uuid.New()),
				expectedStatus: http.StatusNotFound,
				description:    "Non-existent account ID should return 404",
			},
			{
				name:           "nonexistent_client_id",
				queryParams:    "client_id=nonexistent-client-id",
				expectedStatus: http.StatusNotFound,
				description:    "Non-existent client ID should return 404",
			},
			{
				name:           "nonexistent_email_org_combination",
				queryParams:    "client_email=<EMAIL>&client_organization=Nonexistent%20Org",
				expectedStatus: http.StatusNotFound,
				description:    "Non-existent email/org combination should return 404",
			},
			{
				name:           "email_without_organization",
				queryParams:    "client_email=<EMAIL>",
				expectedStatus: http.StatusBadRequest,
				description:    "Email without organization should return 400",
			},
			{
				name:           "organization_without_email",
				queryParams:    "client_organization=Test Org",
				expectedStatus: http.StatusBadRequest,
				description:    "Organization without email should return 400",
			},
			{
				name:           "multiple_parameter_combinations",
				queryParams:    fmt.Sprintf("id=%s&client_id=%s", serviceAccount1.AccountID, serviceAccount2.ClientID),
				expectedStatus: http.StatusBadRequest,
				description:    "Multiple parameter combinations should return 400",
			},
			{
				name: "id_with_email_org",
				queryParams: fmt.Sprintf("id=%s&client_email=%s&client_organization=%s",
					serviceAccount1.AccountID, serviceAccount2.ClientContactEmail, serviceAccount2.ClientOrganization),
				expectedStatus: http.StatusBadRequest,
				description:    "ID with email/org should return 400",
			},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				response := makeGetServiceAccountsRequest(t, baseURL, ownerToken, tt.queryParams)
				defer response.Body.Close()

				if response.StatusCode != tt.expectedStatus {
					t.Errorf("Expected status %d, got %d. %s", tt.expectedStatus, response.StatusCode, tt.description)
				}

				// For error responses, verify error structure
				if tt.expectedStatus >= 400 {
					var errorResponse map[string]any
					if err := json.NewDecoder(response.Body).Decode(&errorResponse); err != nil {
						t.Logf("Failed to decode error response (this might be expected for some error types): %v", err)
						// Some error responses might not be JSON, which is acceptable
					} else {
						if _, exists := errorResponse["error_code"]; !exists {
							t.Error("Error response missing 'error_code' field")
						}
						if _, exists := errorResponse["message"]; !exists {
							t.Error("Error response missing 'message' field")
						}
					}
				}
			})
		}
	})
}

// ServiceAccountTestData represents a test service account
type ServiceAccountTestData struct {
	AccountID          uuid.UUID
	ClientID           string
	ClientContactEmail string
	ClientOrganization string
}

// createTestServiceAccount creates a service account for testing with specific email and organization
func createTestServiceAccount(t *testing.T, ctx context.Context, queries *database.Queries, email, organization string) ServiceAccountTestData {
	// Create account record
	account, err := queries.CreateServiceAccountAccount(ctx)
	if err != nil {
		t.Fatalf("Failed to create service account: %v", err)
	}

	clientID := fmt.Sprintf("test-client-%s", account.ID.String()[:8])

	// Create service account record
	_, err = queries.CreateServiceAccount(ctx, database.CreateServiceAccountParams{
		AccountID:          account.ID,
		ClientID:           clientID,
		ClientContactEmail: email,
		ClientOrganization: organization,
	})
	if err != nil {
		t.Fatalf("Failed to create service account record: %v", err)
	}

	return ServiceAccountTestData{
		AccountID:          account.ID,
		ClientID:           clientID,
		ClientContactEmail: email,
		ClientOrganization: organization,
	}
}

// getAccessToken creates an access token for testing
func getAccessToken(t *testing.T, authService *auth.AuthService, accountID uuid.UUID) string {
	ctx := auth.ContextWithAccountID(context.Background(), accountID)
	tokenResponse, err := authService.CreateAccessToken(ctx)
	if err != nil {
		t.Fatalf("Failed to create access token: %v", err)
	}
	return tokenResponse.AccessToken
}

// makeGetServiceAccountsRequest makes a GET request to the service accounts endpoint
func makeGetServiceAccountsRequest(t *testing.T, baseURL, token, queryParams string) *http.Response {
	requestURL := fmt.Sprintf("%s/api/admin/service-accounts", baseURL)
	if queryParams != "" {
		requestURL += "?" + queryParams
	}

	req, err := http.NewRequest("GET", requestURL, nil)
	if err != nil {
		t.Fatalf("Failed to create request: %v", err)
	}

	if token != "" {
		req.Header.Set("Authorization", "Bearer "+token)
	}

	client := &http.Client{Timeout: 10 * time.Second}
	response, err := client.Do(req)
	if err != nil {
		t.Fatalf("Failed to make request: %v", err)
	}

	return response
}

// makeGetServiceAccountsRequestWithParams makes a GET request with properly encoded query parameters
func makeGetServiceAccountsRequestWithParams(t *testing.T, baseURL, token string, params map[string]string) *http.Response {
	requestURL := fmt.Sprintf("%s/api/admin/service-accounts", baseURL)

	if len(params) > 0 {
		values := url.Values{}
		for key, value := range params {
			values.Add(key, value)
		}
		requestURL += "?" + values.Encode()
	}

	req, err := http.NewRequest("GET", requestURL, nil)
	if err != nil {
		t.Fatalf("Failed to create request: %v", err)
	}

	if token != "" {
		req.Header.Set("Authorization", "Bearer "+token)
	}

	client := &http.Client{Timeout: 10 * time.Second}
	response, err := client.Do(req)
	if err != nil {
		t.Fatalf("Failed to make request: %v", err)
	}

	return response
}
